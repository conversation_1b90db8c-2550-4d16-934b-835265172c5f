import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Phone, X, ArrowRight, Shield, AlertCircle, Loader2 } from 'lucide-react';
import OtpInput from 'react-otp-input';
import { sendOTP, verifyOTP } from '../../firebase/auth';
import { useAuth } from '../../contexts/AuthContext';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function AuthModal({ isOpen, onClose, onSuccess }: AuthModalProps) {
  const { createUserProfile } = useAuth();
  const [step, setStep] = useState<'phone' | 'otp' | 'details'>('phone');
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [userDetails, setUserDetails] = useState({
    name: '',
    address: '',
    pincode: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [recaptchaError, setRecaptchaError] = useState<string | null>(null);
  const [confirmationResult, setConfirmationResult] = useState<any>(null);
  const [userExists, setUserExists] = useState(false);

  const recaptchaContainerRef = useRef<HTMLDivElement>(null);

  // Format phone number to include country code if not present
  const formatPhoneNumber = (phoneNumber: string) => {
    if (!phoneNumber) return '';

    // Special case for Firebase test numbers in development
    if (process.env.NODE_ENV === 'development' && phoneNumber.startsWith('+1650555')) {
      return phoneNumber; // Return test phone number as is
    }

    // Remove any non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');

    // If it doesn't start with +91 (India), add it
    if (!digits.startsWith('91')) {
      return `+91${digits}`;
    }

    return `+${digits}`;
  };

  // Add this function to help with development testing
  const getTestPhoneNumber = () => {
    if (process.env.NODE_ENV === 'development') {
      // Firebase test phone numbers
      return '+16505551234'; // This will always work with OTP 123456 in test mode
    }
    return phone;
  };

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setRecaptchaError(null);
    setLoading(true);

    try {
      // Use test phone number in development
      const phoneToUse = process.env.NODE_ENV === 'development' 
        ? getTestPhoneNumber() 
        : formatPhoneNumber(phone);
      
      // Send OTP
      const result = await sendOTP(phoneToUse, 'recaptcha-container');

      if (result.success) {
        setConfirmationResult(result.confirmationResult);
        setStep('otp');
      } else {
        // Handle specific error codes
        if (result.errorCode === 'auth/invalid-app-credential') {
          setRecaptchaError('reCAPTCHA verification failed. Please try again.');
        } else if (result.errorCode === 'auth/captcha-check-failed') {
          setRecaptchaError('reCAPTCHA check failed. Please refresh and try again.');
        } else if (result.errorCode === 'auth/invalid-phone-number') {
          setError('Invalid phone number format. Please check and try again.');
        } else if (result.errorCode === 'auth/too-many-requests') {
          setError('Too many requests. Please try again later or use a different phone number.');
        } else {
          setError('Failed to send OTP. Please try again.');
        }
      }
    } catch (err) {
      console.error('Error in phone submission:', err);
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      // In development, any OTP will work with test phone numbers
      const otpToUse = process.env.NODE_ENV === 'development' ? '123456' : otp;
      
      const result = await verifyOTP(confirmationResult, otpToUse);

      if (result.success) {
        if (result.userExists) {
          onSuccess();
        } else {
          setStep('details');
        }
      } else {
        setError('Invalid OTP. Please try again.');
      }
    } catch (err) {
      console.error('Error in OTP verification:', err);
      setError('Failed to verify OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDetailsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      // Validate inputs
      if (!userDetails.name.trim()) {
        setError('Please enter your name');
        setLoading(false);
        return;
      }

      if (!userDetails.address.trim()) {
        setError('Please enter your address');
        setLoading(false);
        return;
      }

      if (!userDetails.pincode || userDetails.pincode.length !== 6) {
        setError('Please enter a valid 6-digit pincode');
        setLoading(false);
        return;
      }

      console.log('Creating user profile with:', {
        name: userDetails.name,
        phone: formatPhoneNumber(phone),
        address: userDetails.address,
        pincode: userDetails.pincode
      });
      
      // Create user profile in Firestore with more detailed address
      const addressData = {
        addressID: 1, // First address ID
        addressLines: userDetails.address,
        addressName: 'Home',
        landmark: '',
        pincode: parseInt(userDetails.pincode),
        phoneNumber: formatPhoneNumber(phone),
        branchCode: 'MAIN', // Default branch code
        branchName: 'Main Branch' // Default branch name
      };
      
      console.log('Address data being sent:', addressData);
      
      await createUserProfile(userDetails.name, formatPhoneNumber(phone), addressData);
      
      console.log('User profile created successfully');
      onSuccess();
    } catch (err) {
      console.error('Error creating user profile:', err);
      setError(`Failed to create profile: ${err.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.95 }}
            className="bg-white rounded-2xl p-6 w-full max-w-lg relative overflow-hidden"
          >
            <button
              onClick={onClose}
              className="absolute right-4 top-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
              disabled={loading}
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>

            {/* Hidden recaptcha container */}
            <div 
              id="recaptcha-container" 
              ref={recaptchaContainerRef} 
              className="min-h-[60px] w-full"
            ></div>

            {step === 'phone' && (
              <div>
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Phone className="w-8 h-8 text-primary" />
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800 mb-2">Welcome Back!</h2>
                  <p className="text-gray-600">Enter your phone number to continue</p>
                </div>

                {error && (
                  <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg flex items-center gap-2">
                    <AlertCircle className="w-5 h-5" />
                    <span>{error}</span>
                  </div>
                )}

                {recaptchaError && (
                  <div className="mb-4 p-3 bg-yellow-50 text-yellow-700 rounded-lg flex items-center gap-2">
                    <AlertCircle className="w-5 h-5" />
                    <span>{recaptchaError}</span>
                  </div>
                )}

                <form onSubmit={handlePhoneSubmit}>
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      placeholder="Enter your phone number"
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/20"
                      required
                      disabled={loading}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Format: 9876543210 (Country code will be added automatically)
                    </p>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-primary text-white py-3 rounded-xl font-semibold flex items-center justify-center gap-2"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        Sending OTP...
                      </>
                    ) : (
                      <>
                        Continue
                        <ArrowRight className="w-5 h-5" />
                      </>
                    )}
                  </button>
                </form>

                <p className="text-center text-sm text-gray-500 mt-6">
                  By continuing, you agree to our{' '}
                  <button className="text-primary">Terms of Service</button> and{' '}
                  <button className="text-primary">Privacy Policy</button>
                </p>
              </div>
            )}

            {step === 'otp' && (
              <div>
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-primary" />
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-800 mb-2">Verify OTP</h2>
                  <p className="text-gray-600">Enter the OTP sent to {formatPhoneNumber(phone)}</p>
                </div>

                {error && (
                  <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg flex items-center gap-2">
                    <AlertCircle className="w-5 h-5" />
                    <span>{error}</span>
                  </div>
                )}

                <form onSubmit={handleOtpSubmit} className="space-y-6">
                  <div className="flex justify-center">
                    <OtpInput
                      value={otp}
                      onChange={setOtp}
                      numInputs={6}
                      renderInput={(props) => (
                        <input
                          {...props}
                          className="!w-12 h-12 !mx-1 text-center border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20"
                          disabled={loading}
                        />
                      )}
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-primary text-white py-3 rounded-xl font-semibold"
                    disabled={loading || otp.length !== 6}
                  >
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        Verifying...
                      </div>
                    ) : (
                      'Verify OTP'
                    )}
                  </button>

                  <button
                    type="button"
                    className="w-full text-primary font-medium"
                    onClick={() => {
                      setOtp('');
                      setStep('phone');
                    }}
                    disabled={loading}
                  >
                    Resend OTP
                  </button>
                </form>
              </div>
            )}

            {step === 'details' && (
              <div>
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-semibold text-gray-800 mb-2">Complete Your Profile</h2>
                  <p className="text-gray-600">We need a few more details</p>
                </div>

                {error && (
                  <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg flex items-center gap-2">
                    <AlertCircle className="w-5 h-5" />
                    <span>{error}</span>
                  </div>
                )}

                <form onSubmit={handleDetailsSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={userDetails.name}
                      onChange={(e) => setUserDetails({ ...userDetails, name: e.target.value })}
                      placeholder="Enter your full name"
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/20"
                      required
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Delivery Address
                    </label>
                    <textarea
                      value={userDetails.address}
                      onChange={(e) => setUserDetails({ ...userDetails, address: e.target.value })}
                      placeholder="Enter your delivery address"
                      rows={3}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/20"
                      required
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Pincode
                    </label>
                    <input
                      type="text"
                      value={userDetails.pincode}
                      onChange={(e) => setUserDetails({ ...userDetails, pincode: e.target.value })}
                      placeholder="Enter your pincode"
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/20"
                      required
                      disabled={loading}
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-primary text-white py-3 rounded-xl font-semibold mt-6"
                    disabled={loading}
                  >
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        Creating Profile...
                      </div>
                    ) : (
                      'Complete Profile'
                    )}
                  </button>
                </form>
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
