// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getFirestore } from "firebase/firestore";
import { getAuth } from "firebase/auth";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCH3NSH_EB-AX9hunvkdqrj0vS34IbDKpQ",
  authDomain: "vrisham-cad24.firebaseapp.com",
  projectId: "vrisham-cad24",
  storageBucket: "vrisham-cad24.appspot.com",
  messagingSenderId: "404878904416",
  appId: "1:404878904416:web:e48d6d054a35ecb5de8705",
  measurementId: "G-HG1367QCEK"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);
const db = getFirestore(app);
const auth = getAuth(app);
const storage = getStorage(app);

// Enable test mode for development
if (process.env.NODE_ENV === 'development') {
  // This line enables test mode for phone authentication
  auth.settings.appVerificationDisabledForTesting = true;
}

export { app, analytics, db, auth, storage };


