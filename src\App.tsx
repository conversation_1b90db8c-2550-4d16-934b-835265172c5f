import { BrowserRouter, Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { Header } from './components/Header';
import { Hero } from './components/Hero';
import { UrgentHarvest } from './components/UrgentHarvest';
import { CategoryButtons } from './components/CategoryButtons';
import { HowItWorks } from './components/HowItWorks';
import { PopularStaples } from './components/PopularStaples';
import { FounderLetter } from './components/FounderLetter';
import { BottomNav } from './components/BottomNav';
import { ProductDetails } from './components/ProductDetails';
import { Cart } from './components/Cart';
import { Checkout } from './components/Checkout';
import { Success } from './components/Success';
import { Categories } from './components/Categories';
import { SearchBar } from './components/SearchBar';
import { Profile } from './components/Profile';
import { Login } from './components/Login';
import { ProtectedRoute } from './components/ProtectedRoute';
import { AuthProvider } from './contexts/AuthContext';
import { useAuth } from './contexts/AuthContext';
import { CartProvider } from './contexts/CartContext';
import { About } from './pages/About';
import { Contact } from './pages/Contact';
import { FAQ } from './pages/FAQ';
import { Terms } from './pages/Terms';

function MainLayout() {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const isHome = location.pathname === '/';

  const showSearch = ['/', '/categories'].includes(location.pathname);

  const hideBottomNav = ['/product', '/cart', '/checkout', '/success', '/login', '/about', '/contact', '/faq', '/terms'].some(path =>
    location.pathname.startsWith(path)
  );

  const hideHeader = ['/product', '/cart', '/checkout', '/success', '/login', '/about', '/contact', '/faq', '/terms'].some(path =>
    location.pathname.startsWith(path)
  );

  // No longer needed

  return (
    <div className="min-h-screen bg-background">
      {!hideHeader && <Header />}
      {showSearch && <SearchBar />}
      <div className={`${showSearch ? 'pt-[60px]' : 'pt-[60px]'} ${hideHeader ? 'pt-0' : ''} pb-20`}>
        {isHome && isAuthenticated ? (
          <>
            <Hero />
            <UrgentHarvest />
            <CategoryButtons />
            <HowItWorks />
            <PopularStaples />
            <FounderLetter />
          </>
        ) : null}
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/product/:id" element={<ProductDetails />} />

          {/* Home route */}
          <Route path="/" element={
            isAuthenticated ? (
              <div>{/* Home content is rendered in the MainLayout */}</div>
            ) : (
              <Navigate to="/login" />
            )
          } />
          <Route path="/categories" element={
            <ProtectedRoute>
              <Categories />
            </ProtectedRoute>
          } />
          <Route path="/cart" element={
            <ProtectedRoute>
              <Cart />
            </ProtectedRoute>
          } />
          <Route path="/checkout" element={
            <ProtectedRoute>
              <Checkout />
            </ProtectedRoute>
          } />
          <Route path="/success" element={
            <ProtectedRoute>
              <Success />
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          } />

          {/* Info pages */}
          <Route path="/about" element={
            <ProtectedRoute>
              <About />
            </ProtectedRoute>
          } />
          <Route path="/contact" element={
            <ProtectedRoute>
              <Contact />
            </ProtectedRoute>
          } />
          <Route path="/faq" element={
            <ProtectedRoute>
              <FAQ />
            </ProtectedRoute>
          } />
          <Route path="/terms" element={
            <ProtectedRoute>
              <Terms />
            </ProtectedRoute>
          } />

          {/* Redirect to login for any other routes */}
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </div>
      {!hideBottomNav && <BottomNav />}
    </div>
  );
}

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <CartProvider>
          <MainLayout />
        </CartProvider>
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;