import {
  signInWithPhoneNumber,
  PhoneAuthProvider,
  RecaptchaVerifier,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import { auth } from './config';
import { getDocument, addDocument, updateDocument, setDocument } from './firestore';
import { User as UserType } from './schema';

// Initialize RecaptchaVerifier
let recaptchaVerifier: RecaptchaVerifier | null = null;

/**
 * Initialize the RecaptchaVerifier
 */
export const initRecaptcha = (containerId: string): RecaptchaVerifier => {
  // Always clear existing recaptcha first
  if (recaptchaVerifier) {
    try {
      recaptchaVerifier.clear();
    } catch (e) {
      console.warn('Failed to clear existing reCAPTCHA:', e);
    }
    recaptchaVerifier = null;
  }

  // Create new RecaptchaVerifier
  recaptchaVerifier = new RecaptchaVerifier(auth, containerId, {
    size: 'invisible',
    callback: () => {
      console.log('reCAPTCHA verified successfully');
    },
    'expired-callback': () => {
      console.log('reCAPTCHA expired');
      recaptchaVerifier = null;
    }
  });

  return recaptchaVerifier;
};

/**
 * Send OTP to the provided phone number
 */
export const sendOTP = async (phoneNumber: string, recaptchaContainerId: string): Promise<any> => {
  try {
    // Make sure phone number is in E.164 format
    const formattedPhone = phoneNumber.startsWith('+') ? phoneNumber : `+${phoneNumber}`;

    // Initialize recaptcha
    const verifier = initRecaptcha(recaptchaContainerId);

    // Render the reCAPTCHA to ensure it's ready
    await verifier.render();

    // Send verification code
    const confirmationResult = await signInWithPhoneNumber(auth, formattedPhone, verifier);
    return { success: true, confirmationResult };
  } catch (error) {
    console.error('Error sending OTP:', error);

    // Clear reCAPTCHA on error
    if (recaptchaVerifier) {
      try {
        recaptchaVerifier.clear();
      } catch (e) {
        console.warn('Failed to clear reCAPTCHA after error:', e);
      }
      recaptchaVerifier = null;
    }

    // Handle specific error codes
    let errorMessage = error.message;
    if (error.code === 'auth/too-many-requests') {
      errorMessage = 'Too many requests. Please try again later or use a different phone number.';
    } else if (error.code === 'auth/invalid-phone-number') {
      errorMessage = 'Invalid phone number format. Please check and try again.';
    }

    return {
      success: false,
      error,
      errorCode: error.code,
      errorMessage
    };
  }
};

/**
 * Verify OTP and sign in the user
 */
export const verifyOTP = async (confirmationResult: any, otp: string): Promise<any> => {
  try {
    // Add timeout promise
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('OTP verification timeout')), 30000)
    );

    // Race between confirmation and timeout
    const userCredential = await Promise.race([
      confirmationResult.confirm(otp),
      timeoutPromise
    ]);

    const user = userCredential.user;

    // Check if user exists in Firestore after authentication
    const userDoc = await getDocument<UserType>('Users', user.uid);
    const userExists = !!userDoc;

    return {
      success: true,
      user: user,
      userExists: userExists
    };
  } catch (error) {
    console.error('Error verifying OTP:', error);
    return { success: false, error };
  }
};

/**
 * Check if a user exists in the database
 */
export const checkUserExists = async (phoneNumber: string): Promise<boolean> => {
  try {
    // Since we're in the authentication flow, we can't rely on Firestore security rules
    // that require authentication. Instead, we'll check if the user exists after they've
    // authenticated with phone number.

    // We'll use the phone auth credential to sign in, and then check if the user has a
    // document in Firestore. If not, we'll need to create one.

    // This function now just returns false, and we'll handle the user creation after
    // successful authentication
    return false;
  } catch (error) {
    console.error('Error checking if user exists:', error);
    return false;
  }
};

/**
 * Create a new user in the database
 */
export const createUser = async (
  uid: string,
  displayName: string,
  phoneNumber: string,
  address?: Partial<UserType['listOfAddress'][0]>
): Promise<string> => {
  try {
    // Create user data object with required fields
    const userData: Partial<UserType> = {
      uid,
      displayName,
      createdTime: new Date() as any, // Will be converted to Timestamp
      isDeactivated: false,
      isNewCustomer: true,
      keywords: generateKeywords(displayName),
      listOfAddress: address ? [address as any] : [],
      role: 'customer',
    };

    // If phoneNumber is provided and address is given, add it to the address
    if (phoneNumber && address) {
      const formattedPhone = phoneNumber.startsWith('+') ? phoneNumber : `+${phoneNumber}`;
      (userData.listOfAddress![0] as any).phoneNumber = formattedPhone;
    }

    console.log('Creating user with data:', userData);

    try {
      // Set document with UID as the document ID
      await setDocument('Users', uid, userData);
      console.log('User created with ID:', uid);
      return uid;
    } catch (error) {
      console.error('Error in setDocument:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

/**
 * Get the current user's data from Firestore
 */
export const getCurrentUserData = async (): Promise<UserType | null> => {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.log('No current user found in Firebase Auth');
      return null;
    }

    console.log('Fetching user data for UID:', currentUser.uid);

    const userData = await getDocument<UserType>('Users', currentUser.uid);

    if (!userData) {
      console.warn('User document not found in Firestore for UID:', currentUser.uid);

      // If user exists in Auth but not in Firestore, create a basic profile
      console.log('Creating basic user profile for authenticated user');

      const basicUserData: Partial<UserType> = {
        uid: currentUser.uid,
        displayName: currentUser.displayName || 'User',
        createdTime: new Date() as any,
        isDeactivated: false,
        isNewCustomer: true,
        keywords: generateKeywords(currentUser.displayName || 'User'),
        listOfAddress: [],
        role: 'customer',
      };

      try {
        await setDocument('Users', currentUser.uid, basicUserData);
        console.log('Basic user profile created');
        return basicUserData as UserType;
      } catch (createError) {
        console.error('Error creating basic user profile:', createError);
        return null;
      }
    }

    console.log('User data fetched successfully:', userData);
    return userData;
  } catch (error) {
    console.error('Error getting current user data:', error);
    return null;
  }
};

/**
 * Update user profile
 */
export const updateUserProfile = async (uid: string, data: Partial<UserType>): Promise<void> => {
  try {
    await updateDocument<UserType>('Users', uid, data);
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw error;
  }
};

/**
 * Sign out the current user
 */
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

/**
 * Listen to auth state changes
 */
export const onAuthChange = (callback: (user: User | null) => void): () => void => {
  return onAuthStateChanged(auth, callback);
};

/**
 * Generate keywords for search functionality
 */
const generateKeywords = (displayName: string): string[] => {
  const name = displayName.toLowerCase();
  const keywords: string[] = [];

  // Generate prefixes
  let currentKeyword = '';
  for (const char of name) {
    currentKeyword += char;
    if (currentKeyword.trim()) {
      keywords.push(currentKeyword);
    }
  }

  // Add full name
  if (!keywords.includes(name)) {
    keywords.push(name);
  }

  // Add name parts (for multi-word names)
  const nameParts = name.split(' ');
  for (const part of nameParts) {
    if (part.trim() && !keywords.includes(part)) {
      keywords.push(part);
    }
  }

  return [...new Set(keywords)]; // Remove duplicates
};
