import React, { useState } from 'react';
import { LocationSelector } from './maps/LocationSelector';
import { LocationResult } from '../utils/location';

export function TestMaps() {
  const [selectedLocation, setSelectedLocation] = useState<LocationResult | null>(null);

  const handleLocationSelect = (location: LocationResult) => {
    setSelectedLocation(location);
    console.log('Selected location:', location);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Google Maps Test</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">Location Selector</h2>
          <LocationSelector
            onLocationSelect={handleLocationSelect}
            variant="card"
          />
        </div>

        {selectedLocation && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Selected Location</h2>
            <div className="space-y-2">
              <p><strong>Address:</strong> {selectedLocation.formattedAddress}</p>
              <p><strong>Coordinates:</strong> {selectedLocation.coordinates.lat}, {selectedLocation.coordinates.lng}</p>
              {selectedLocation.placeId && (
                <p><strong>Place ID:</strong> {selectedLocation.placeId}</p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
