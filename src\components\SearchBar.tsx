import React, { useState, useRef, useEffect } from 'react';
import { Search, X, ArrowRight, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

interface SearchSuggestion {
  type: 'recent' | 'popular';
  text: string;
}

export function SearchBar() {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null);
  const [lastScrollY, setLastScrollY] = useState(0);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const recentSearches: SearchSuggestion[] = [
    { type: 'recent', text: 'Organic Tomatoes' },
    { type: 'recent', text: 'Fresh Spinach' },
    { type: 'recent', text: 'Turmeric Powder' }
  ];

  const popularSearches: SearchSuggestion[] = [
    { type: 'popular', text: 'Seasonal Fruits' },
    { type: 'popular', text: 'Organic Pulses' },
    { type: 'popular', text: 'Cold Pressed Oils' }
  ];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    // Use a proper debounce with a higher threshold to prevent shaking
    let scrollTimeout: NodeJS.Timeout;
    let ticking = false;

    const controlSearchBar = () => {
      const currentScrollY = window.scrollY;

      // Don't update if we're already processing a scroll event
      if (!ticking) {
        // Use requestAnimationFrame for smoother performance
        window.requestAnimationFrame(() => {
          const scrollDifference = Math.abs(currentScrollY - lastScrollY);

          // Only respond to significant scroll changes (15px threshold)
          if (scrollDifference > 15) {
            // Clear any pending timeouts
            if (scrollTimeout) clearTimeout(scrollTimeout);

            if (currentScrollY > lastScrollY + 20) {
              // Scrolling down significantly
              setScrollDirection('down');
              if (!isFocused) {
                setIsMinimized(true);
              }
            } else if (currentScrollY < lastScrollY - 20) {
              // Scrolling up significantly
              setScrollDirection('up');
              setIsMinimized(false);
            }

            // Update last scroll position with a longer delay
            scrollTimeout = setTimeout(() => {
              setLastScrollY(currentScrollY);
            }, 100);
          }

          ticking = false;
        });

        ticking = true;
      }
    };

    // Use passive event listener for better performance
    window.addEventListener('scroll', controlSearchBar, { passive: true });

    return () => {
      window.removeEventListener('scroll', controlSearchBar);
      if (scrollTimeout) clearTimeout(scrollTimeout);
    };
  }, [lastScrollY, isFocused]);

  const handleSearch = async (searchText: string = query) => {
    if (!searchText || !searchText.trim()) return;

    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 500));
    setIsLoading(false);
    setIsFocused(false);
    navigate(`/categories?search=${encodeURIComponent(searchText.trim())}`);
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text);
    handleSearch(suggestion.text);
  };

  return (
    <div
      ref={searchRef}
      className="sticky top-[57px] z-40 will-change-transform transform-gpu"
    >
      <div className="relative">
        <div className="absolute inset-0 bg-white shadow-sm -z-10" />

        <div className={`max-w-2xl mx-auto px-4 transition-height duration-200 ease-out ${isMinimized ? 'py-1' : 'py-3'}`}>
          <div className="relative">
            {/* Search Input */}
            <div
              className="relative transform-gpu"
              onClick={() => {
                if (isMinimized) {
                  setIsMinimized(false);
                  inputRef.current?.focus();
                }
              }}
            >
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onFocus={() => setIsFocused(true)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
                placeholder="Search vegetables, fruits, staples..."
                className={`w-full pl-12 pr-24 bg-white rounded-2xl border transition-colors duration-200 ease-out
                  ${isFocused
                    ? 'border-primary shadow-md'
                    : 'border-primary/30 shadow-sm hover:border-primary'
                  }
                  ${isMinimized && !isFocused ? 'py-2' : 'py-3'}
                  focus:outline-none focus:ring-1 focus:ring-primary/30`}
              />

              {/* Left Icon */}
              <div className="absolute left-4 top-1/2 -translate-y-1/2">
                {isLoading ? (
                  <Loader2 className="w-5 h-5 text-primary animate-spin" />
                ) : (
                  <Search className="w-5 h-5 text-primary" />
                )}
              </div>

              {/* Right Icons */}
              <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-2">
                <AnimatePresence>
                  {query && (
                    <motion.button
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      onClick={() => setQuery('')}
                      className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                      aria-label="Clear search"
                    >
                      <X className="w-4 h-4 text-gray-400" />
                    </motion.button>
                  )}
                </AnimatePresence>

                {/* Search Button */}
                <button
                  onClick={() => handleSearch()}
                  className={`rounded-xl text-white font-medium transition-colors ${
                    query.trim() ? 'bg-primary hover:bg-primary/90' : 'bg-gray-300 cursor-not-allowed'
                  } px-4 py-1.5`}
                  disabled={!query.trim()}
                  aria-label="Search"
                >
                  Search
                </button>
              </div>
            </div>

            {/* Search Suggestions Dropdown */}
            <AnimatePresence>
              {isFocused && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-full left-0 right-0 mt-2 bg-white rounded-2xl shadow-md border border-gray-200 overflow-hidden"
                >
                  {/* Recent Searches */}
                  {recentSearches.length > 0 && (
                    <div className="p-3">
                      <div className="text-xs font-medium text-gray-500 px-3 mb-2">
                        Recent Searches
                      </div>
                      {recentSearches.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion)}
                          className="w-full flex items-center gap-3 p-3 hover:bg-gray-50 rounded-xl text-left transition-colors"
                        >
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <Search className="w-4 h-4 text-gray-500" />
                          </div>
                          <span className="flex-1 text-gray-700">{suggestion.text}</span>
                          <ArrowRight className="w-4 h-4 text-gray-400" />
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Popular Searches */}
                  <div className="border-t border-gray-100 p-3">
                    <div className="text-xs font-medium text-gray-500 px-3 mb-2">
                      Popular Searches
                    </div>
                    {popularSearches.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full flex items-center gap-3 p-3 hover:bg-gray-50 rounded-xl text-left transition-colors"
                      >
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <Search className="w-4 h-4 text-primary" />
                        </div>
                        <span className="flex-1 text-gray-700">{suggestion.text}</span>
                        <ArrowRight className="w-4 h-4 text-gray-400" />
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}