import React, { useState } from 'react';
import { MapPin, Navigation, Loader2 } from 'lucide-react';
import { GoogleMapPicker } from './GoogleMapPicker';
import { getCurrentLocation, LocationResult, LocationCoordinates } from '../../utils/location';

interface LocationSelectorProps {
  onLocationSelect: (location: LocationResult) => void;
  initialLocation?: LocationCoordinates;
  className?: string;
  variant?: 'button' | 'card';
  showCurrentLocationOption?: boolean;
}

export const LocationSelector: React.FC<LocationSelectorProps> = ({
  onLocationSelect,
  initialLocation,
  className = '',
  variant = 'button',
  showCurrentLocationOption = true,
}) => {
  const [isMapOpen, setIsMapOpen] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  const handleGetCurrentLocation = async () => {
    setIsGettingLocation(true);
    try {
      const coordinates = await getCurrentLocation();
      
      // Create a mock geocoder result for current location
      const locationResult: LocationResult = {
        coordinates,
        formattedAddress: 'Current Location',
        placeId: undefined,
      };
      
      onLocationSelect(locationResult);
    } catch (error: any) {
      console.error('Error getting current location:', error);
      // Fallback to map picker if current location fails
      setIsMapOpen(true);
    } finally {
      setIsGettingLocation(false);
    }
  };

  const handleMapLocationSelect = (location: LocationResult) => {
    onLocationSelect(location);
    setIsMapOpen(false);
  };

  if (variant === 'card') {
    return (
      <>
        <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
          <div className="flex items-center space-x-3 mb-3">
            <MapPin className="w-5 h-5 text-[#73338A]" />
            <h3 className="font-medium text-gray-900">Select Location</h3>
          </div>
          
          <div className="space-y-2">
            {showCurrentLocationOption && (
              <button
                onClick={handleGetCurrentLocation}
                disabled={isGettingLocation}
                className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-[#73338A] text-white rounded-lg hover:bg-[#5a2a6b] transition-colors disabled:opacity-50"
              >
                {isGettingLocation ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Navigation className="w-4 h-4" />
                )}
                <span>Use Current Location</span>
              </button>
            )}
            
            <button
              onClick={() => setIsMapOpen(true)}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-[#73338A] text-[#73338A] rounded-lg hover:bg-[#73338A] hover:text-white transition-colors"
            >
              <MapPin className="w-4 h-4" />
              <span>Select on Map</span>
            </button>
          </div>
        </div>

        <GoogleMapPicker
          isOpen={isMapOpen}
          onClose={() => setIsMapOpen(false)}
          onLocationSelect={handleMapLocationSelect}
          initialLocation={initialLocation}
        />
      </>
    );
  }

  return (
    <>
      <div className={`flex space-x-2 ${className}`}>
        {showCurrentLocationOption && (
          <button
            onClick={handleGetCurrentLocation}
            disabled={isGettingLocation}
            className="flex items-center space-x-2 px-3 py-2 bg-[#73338A] text-white rounded-lg hover:bg-[#5a2a6b] transition-colors disabled:opacity-50 text-sm"
          >
            {isGettingLocation ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Navigation className="w-4 h-4" />
            )}
            <span>Current Location</span>
          </button>
        )}
        
        <button
          onClick={() => setIsMapOpen(true)}
          className="flex items-center space-x-2 px-3 py-2 border border-[#73338A] text-[#73338A] rounded-lg hover:bg-[#73338A] hover:text-white transition-colors text-sm"
        >
          <MapPin className="w-4 h-4" />
          <span>Select on Map</span>
        </button>
      </div>

      <GoogleMapPicker
        isOpen={isMapOpen}
        onClose={() => setIsMapOpen(false)}
        onLocationSelect={handleMapLocationSelect}
        initialLocation={initialLocation}
      />
    </>
  );
};
