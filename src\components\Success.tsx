import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';
import { CheckCircle2, Truck, Calendar, MapPin, ArrowRight, Clock, Package, Ticket } from 'lucide-react';

export function Success() {
  const location = useLocation();
  const navigate = useNavigate();
  const {
    orderId,
    orderNumber,
    total,
    deliveryPreference,
    paymentMethod,
    address,
    items,
    coupon,
    couponDiscount
  } = location.state || {};

  useEffect(() => {
    if (!location.state) {
      navigate('/');
    }
  }, [location.state, navigate]);

  if (!location.state) return null;

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-2xl mx-auto pt-8">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6"
        >
          <CheckCircle2 className="w-12 h-12 text-primary" />
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="text-center mb-8"
        >
          <h1 className="font-display text-3xl font-bold text-gray-800 mb-2">
            Order Confirmed!
          </h1>
          <p className="text-gray-600 mb-2">
            Thank you for shopping with Vrisham Organic
          </p>
          {orderNumber && (
            <p className="text-sm text-gray-500">
              Order #{orderNumber}
            </p>
          )}
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl p-6 shadow-sm mb-6"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Clock className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="font-medium text-lg">Order Status</h2>
              <p className="text-gray-600">
                {paymentMethod === 'cod' ? 'Order placed successfully' : 'Payment received'}
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center py-2 border-t border-dashed">
              <span className="text-gray-600">Order Amount</span>
              <span className="font-medium">₹{total.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center py-2 border-t border-dashed">
              <span className="text-gray-600">Payment Method</span>
              <span className="font-medium">
                {paymentMethod === 'upi' ? 'UPI Payment' :
                 paymentMethod === 'card' ? 'Card Payment' : 'Cash on Delivery'}
              </span>
            </div>
          </div>
        </motion.div>

        {/* Ordered Items */}
        {items && items.length > 0 && (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl p-6 shadow-sm mb-6"
          >
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Package className="w-6 h-6 text-primary" />
              </div>
              <div>
                <h2 className="font-medium text-lg">Items Ordered</h2>
                <p className="text-gray-600">{items.length} item{items.length !== 1 ? 's' : ''} in your order</p>
              </div>
            </div>

            <div className="space-y-3">
              {items.map((item, index) => (
                <div
                  key={item.id}
                  className={`flex items-center gap-3 p-3 rounded-lg bg-gray-50 ${
                    index !== items.length - 1 ? 'mb-2' : ''
                  }`}
                >
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-800 text-sm">{item.name}</h3>
                    <p className="text-gray-500 text-xs">{item.nameTamil}</p>
                    <p className="text-gray-600 text-sm">
                      {item.quantity} {item.unit} × ₹{item.price.toFixed(2)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-primary">
                      ₹{(item.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Coupon Information */}
        {coupon && couponDiscount > 0 && (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="bg-green-50 border border-green-200 rounded-xl p-6 shadow-sm mb-6"
          >
            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <Ticket className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h2 className="font-medium text-lg text-green-800">Coupon Applied</h2>
                <p className="text-green-600">You saved ₹{couponDiscount.toFixed(2)} on this order!</p>
              </div>
            </div>

            <div className="p-3 bg-green-100 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-green-800">{coupon.code}</p>
                  {coupon.description && (
                    <p className="text-sm text-green-600">{coupon.description}</p>
                  )}
                </div>
                <div className="text-right">
                  <p className="font-medium text-green-800">-₹{couponDiscount.toFixed(2)}</p>
                  <p className="text-xs text-green-600">
                    {coupon.type === 'FLAT' ? 'Flat discount' : `${coupon.percent}% off`}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl p-6 shadow-sm mb-8"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <MapPin className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="font-medium text-lg">Delivery Address</h2>
              <p className="text-gray-600">{address.name}</p>
            </div>
          </div>

          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-gray-600">{address.address}</p>
            <p className="text-gray-600">{address.pincode}</p>
            <p className="text-gray-600">{address.phone}</p>
          </div>
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="flex gap-4"
        >
          <button
            onClick={() => navigate('/orders')}
            className="flex-1 py-4 bg-white border-2 border-primary text-primary rounded-xl font-semibold flex items-center justify-center gap-2"
          >
            Track Order
          </button>
          <button
            onClick={() => navigate('/')}
            className="flex-1 py-4 bg-primary text-white rounded-xl font-semibold flex items-center justify-center gap-2"
          >
            Continue Shopping
            <ArrowRight className="w-5 h-5" />
          </button>
        </motion.div>
      </div>
    </div>
  );
}