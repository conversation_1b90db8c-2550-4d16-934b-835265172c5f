import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Phone, User, MapPin, ArrowRight, Check, Loader2, AlertCircle } from 'lucide-react';
import OtpInput from 'react-otp-input';
import { sendOTP, verifyOTP } from '../firebase/auth';
import { useAuth } from '../contexts/AuthContext';
import { Address } from '../firebase/schema';

// Step enum to track the login flow
enum LoginStep {
  PHONE_INPUT,
  OTP_VERIFICATION,
  NEW_USER_DETAILS
}

export function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, isLoading, createUserProfile, userData } = useAuth();
  const [loginStep, setLoginStep] = useState<LoginStep>(LoginStep.PHONE_INPUT);

  // Form states
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [name, setName] = useState('');
  const [addressName, setAddressName] = useState('Home');
  const [addressLines, setAddressLines] = useState('');
  const [landmark, setLandmark] = useState('');
  const [pincode, setPincode] = useState('');

  // UI states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [confirmationResult, setConfirmationResult] = useState<any>(null);
  const [countdown, setCountdown] = useState(0);

  // Refs
  const recaptchaContainerRef = useRef<HTMLDivElement>(null);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading && userData) {
      // Get the redirect path from location state or default to home
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, isLoading, userData, navigate, location]);

  // Handle countdown for resend OTP
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Format phone number with country code
  const formatPhoneNumber = (phone: string): string => {
    // Special case for Firebase test numbers in development
    if (process.env.NODE_ENV === 'development' && phone.startsWith('+1650555')) {
      return phone; // Return test phone number as is
    }

    // Remove any non-digit characters
    const digits = phone.replace(/\D/g, '');

    // Add India country code (+91) if not present
    if (digits.length === 10) {
      return `+91${digits}`;
    } else if (digits.startsWith('91') && digits.length === 12) {
      return `+${digits}`;
    }

    return phone;
  };

  // Handle phone number submission
  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Special case for Firebase test numbers in development
    if (process.env.NODE_ENV === 'development' && phoneNumber.startsWith('+1650555')) {
      setIsSubmitting(true);
      try {
        // Send OTP using test number
        const result = await sendOTP(phoneNumber, 'recaptcha-container');
        if (result.success) {
          setConfirmationResult(result.confirmationResult);
          setLoginStep(LoginStep.OTP_VERIFICATION);
          setCountdown(30);
        } else {
          setError(`Failed to send OTP: ${result.errorMessage || 'Unknown error'}`);
        }
      } catch (err) {
        console.error('Error sending OTP:', err);
        setError('Failed to send OTP. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
      return;
    }

    // Validate phone number for non-test numbers
    const formattedPhone = formatPhoneNumber(phoneNumber);
    if (formattedPhone.length < 13) {
      setError('Please enter a valid 10-digit phone number');
      return;
    }

    setIsSubmitting(true);

    try {
      // Send OTP
      const result = await sendOTP(formattedPhone, 'recaptcha-container');

      if (result.success) {
        setConfirmationResult(result.confirmationResult);
        setLoginStep(LoginStep.OTP_VERIFICATION);
        setCountdown(30); // Start 30 second countdown for resend
      } else {
        setError(`Failed to send OTP: ${result.errorMessage || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('Error sending OTP:', err);
      setError('Failed to send OTP. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle OTP verification
  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // For development with test phone numbers, accept any 6-digit OTP
    const isTestPhoneNumber = process.env.NODE_ENV === 'development' && 
                             phoneNumber.startsWith('+1650555');
    
    if (!isTestPhoneNumber && otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }

    setIsSubmitting(true);

    try {
      // For test phone numbers in development, use '123456' as the OTP
      const otpToVerify = isTestPhoneNumber ? '123456' : otp;
      
      // Verify OTP
      const result = await verifyOTP(confirmationResult, otpToVerify);

      if (result.success) {
        if (result.userExists) {
          // User exists, redirect to home or previous page
          const from = location.state?.from?.pathname || '/';
          navigate(from, { replace: true });
        } else {
          // New user, collect additional information
          setLoginStep(LoginStep.NEW_USER_DETAILS);
        }
      } else {
        setError('Invalid OTP. Please try again.');
      }
    } catch (err) {
      console.error('Error verifying OTP:', err);
      setError('Failed to verify OTP. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle new user details submission
  const handleNewUserSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form
    if (!name.trim()) {
      setError('Please enter your name');
      return;
    }

    if (!addressLines.trim()) {
      setError('Please enter your address');
      return;
    }

    if (!pincode.trim() || pincode.length !== 6) {
      setError('Please enter a valid 6-digit pincode');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create new user profile with address
      const newAddress: Partial<Address> = {
        addressID: 1, // First address
        addressName,
        addressLines,
        landmark,
        pincode: parseInt(pincode),
        phoneNumber: formatPhoneNumber(phoneNumber),
        branchCode: 'MAIN', // Default branch
        branchName: 'Main Branch'
      };

      await createUserProfile(name, formatPhoneNumber(phoneNumber), newAddress);

      // Redirect to home or previous page
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    } catch (err) {
      console.error('Error creating user profile:', err);
      setError('Failed to create user profile. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (countdown > 0) return;

    setError(null);
    setIsSubmitting(true);

    try {
      // Resend OTP
      const result = await sendOTP(formatPhoneNumber(phoneNumber), 'recaptcha-container');

      if (result.success) {
        setConfirmationResult(result.confirmationResult);
        setCountdown(30); // Reset countdown
      } else {
        setError(`Failed to resend OTP: ${result.errorMessage || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('Error resending OTP:', err);
      setError('Failed to resend OTP. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render different steps based on login flow
  const renderStep = () => {
    switch (loginStep) {
      case LoginStep.PHONE_INPUT:
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <h1 className="text-2xl font-bold text-gray-800 mb-6">Login with Phone</h1>
            <p className="text-gray-600 mb-8">
              Enter your phone number to receive a one-time password
            </p>

            <form onSubmit={handlePhoneSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <Phone className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="Enter your 10-digit number"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-colors"
                    maxLength={10}
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  We'll send you a 6-digit OTP to verify your number
                </p>
              </div>

              <button
                type="submit"
                disabled={isSubmitting || phoneNumber.length !== 10}
                className={`w-full py-3 px-4 rounded-xl font-medium flex items-center justify-center gap-2 ${
                  isSubmitting || phoneNumber.length !== 10
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-primary text-white hover:bg-primary/90'
                } transition-colors`}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    Sending OTP...
                  </>
                ) : (
                  <>
                    Continue
                    <ArrowRight className="h-5 w-5" />
                  </>
                )}
              </button>
            </form>

            {/* Hidden recaptcha container */}
            <div id="recaptcha-container" ref={recaptchaContainerRef}></div>
          </motion.div>
        );

      case LoginStep.OTP_VERIFICATION:
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <h1 className="text-2xl font-bold text-gray-800 mb-6">Verify OTP</h1>
            <p className="text-gray-600 mb-8">
              We've sent a 6-digit code to {formatPhoneNumber(phoneNumber)}
            </p>

            <form onSubmit={handleOtpSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enter OTP
                </label>
                <OtpInput
                  value={otp}
                  onChange={setOtp}
                  numInputs={6}
                  renderSeparator={<span className="w-2"></span>}
                  renderInput={(props) => (
                    <input
                      {...props}
                      className="w-12 h-12 text-center border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-colors"
                    />
                  )}
                  containerStyle="flex justify-center gap-2"
                />
                <div className="flex justify-between items-center mt-4">
                  <button
                    type="button"
                    onClick={() => setLoginStep(LoginStep.PHONE_INPUT)}
                    className="text-sm text-primary hover:text-primary/80"
                  >
                    Change phone number
                  </button>
                  <button
                    type="button"
                    onClick={handleResendOTP}
                    disabled={countdown > 0}
                    className={`text-sm ${
                      countdown > 0 ? 'text-gray-400' : 'text-primary hover:text-primary/80'
                    }`}
                  >
                    {countdown > 0 ? `Resend in ${countdown}s` : 'Resend OTP'}
                  </button>
                </div>
              </div>

              <button
                type="submit"
                disabled={isSubmitting || otp.length !== 6}
                className={`w-full py-3 px-4 rounded-xl font-medium flex items-center justify-center gap-2 ${
                  isSubmitting || otp.length !== 6
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-primary text-white hover:bg-primary/90'
                } transition-colors`}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    Verifying...
                  </>
                ) : (
                  <>
                    Verify & Continue
                    <ArrowRight className="h-5 w-5" />
                  </>
                )}
              </button>
            </form>
          </motion.div>
        );

      case LoginStep.NEW_USER_DETAILS:
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <h1 className="text-2xl font-bold text-gray-800 mb-6">Complete Your Profile</h1>
            <p className="text-gray-600 mb-8">
              Please provide your details to complete registration
            </p>

            <form onSubmit={handleNewUserSubmit}>
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Name
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <User className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Enter your full name"
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-colors"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address Type
                  </label>
                  <div className="flex gap-2">
                    {['Home', 'Work', 'Other'].map((type) => (
                      <button
                        key={type}
                        type="button"
                        onClick={() => setAddressName(type)}
                        className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
                          addressName === type
                            ? 'bg-primary/10 text-primary border border-primary/30'
                            : 'bg-gray-100 text-gray-700 border border-gray-200'
                        }`}
                      >
                        {addressName === type && <Check className="h-4 w-4" />}
                        {type}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <MapPin className="h-5 w-5 text-gray-400" />
                    </div>
                    <textarea
                      value={addressLines}
                      onChange={(e) => setAddressLines(e.target.value)}
                      placeholder="Enter your complete address"
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-colors"
                      rows={3}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Landmark (Optional)
                    </label>
                    <input
                      type="text"
                      value={landmark}
                      onChange={(e) => setLandmark(e.target.value)}
                      placeholder="Nearby landmark"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-colors"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Pincode
                    </label>
                    <input
                      type="text"
                      value={pincode}
                      onChange={(e) => setPincode(e.target.value.replace(/\D/g, ''))}
                      placeholder="6-digit pincode"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-colors"
                      maxLength={6}
                      required
                    />
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={isSubmitting || !name.trim() || !addressLines.trim() || pincode.length !== 6}
                className={`w-full py-3 px-4 rounded-xl font-medium flex items-center justify-center gap-2 ${
                  isSubmitting || !name.trim() || !addressLines.trim() || pincode.length !== 6
                    ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                    : 'bg-primary text-white hover:bg-primary/90'
                } transition-colors`}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  <>
                    Complete Registration
                    <ArrowRight className="h-5 w-5" />
                  </>
                )}
              </button>
            </form>
          </motion.div>
        );
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-lg p-8">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <img
            src="https://www.vrisham.com/assets/images/vrisham-logo.svg"
            alt="Vrisham Organic"
            className="h-12"
          />
        </div>

        {/* Error message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 rounded-xl flex items-start gap-3 text-red-700">
            <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
            <p className="text-sm">{error}</p>
          </div>
        )}

        {/* Login steps */}
        <AnimatePresence mode="wait">
          {renderStep()}
        </AnimatePresence>
      </div>
    </div>
  );
}
