import React from 'react';
import { Trash2, AlertCircle, ArrowLeft, Plus, Minus, Truck } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';

export function Cart() {
  const navigate = useNavigate();
  const { items, removeFromCart, updateQuantity, subtotal } = useCart();

  // Calculate expected delivery date based on product type
  const getDeliveryInfo = (type: 'pre-order' | 'in-stock' | undefined) => {
    const today = new Date();

    if (type === 'in-stock') {
      // In-stock items delivered next day
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Check if tomorrow is a weekend (Saturday or Sunday)
      const dayOfWeek = tomorrow.getDay();
      if (dayOfWeek === 0) { // Sunday
        return "Delivered on Monday";
      } else if (dayOfWeek === 6) { // Saturday
        return "Delivered on Monday";
      } else {
        return "Delivered Tomorrow";
      }
    } else {
      // Pre-order items take 2-3 days
      const deliveryDate = new Date(today);
      deliveryDate.setDate(deliveryDate.getDate() + 3);

      // Format the date as "Delivered by [day of week]"
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayName = days[deliveryDate.getDay()];

      return `Delivered by ${dayName}`;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-md z-50 p-4">
        <div className="flex items-center">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-gray-800"
          >
            <ArrowLeft className="w-6 h-6 mr-2" />
            <span className="font-medium">Back</span>
          </button>
        </div>
      </div>

      <div className="pt-16 pb-32 md:pb-24 p-4 md:max-w-2xl md:mx-auto">
        <h1 className="font-display text-2xl font-bold text-gray-800 mb-6">Your Cart</h1>

        <AnimatePresence>
          {items.map((item) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, x: -100 }}
              className="bg-white border border-primary/20 rounded-xl p-4 shadow-sm hover:shadow-primary/10 mb-4"
            >
              <div className="flex gap-4">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-24 h-24 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium text-gray-800">{item.name}</h3>
                      <p className="text-gray-500 text-sm">{item.nameTamil}</p>
                      {item.selectedSize && (
                        <p className="text-sm text-gray-600 mt-1">
                          Size: {item.selectedSize.label} ({item.selectedSize.weightRange})
                        </p>
                      )}
                    </div>
                    <button
                      onClick={() => removeFromCart(item.id)}
                      className="text-gray-400 hover:text-red-500 transition-colors p-2"
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="mt-3 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => updateQuantity(item.id, -1)}
                        className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="font-medium w-12 text-center">
                        {item.quantity} {item.unit}
                      </span>
                      <button
                        onClick={() => updateQuantity(item.id, 1)}
                        className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                    <span className="font-medium text-primary">
                      ₹{(item.price * item.quantity).toFixed(2)}
                    </span>
                  </div>

                  {/* Delivery Information */}
                  <div className="mt-3 flex items-center gap-1.5 text-sm">
                    <Truck className="w-4 h-4 text-primary" />
                    <span className={`${item.type === 'in-stock' ? 'text-blue-600' : 'text-amber-600'} font-medium`}>
                      {getDeliveryInfo(item.type)}
                    </span>
                  </div>

                  {item.status === 'pending' && (
                    <div className="mt-1.5 flex items-center gap-1 text-secondary text-sm">
                      <AlertCircle className="w-4 h-4" />
                      <span>Pending Confirmation</span>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {items.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-600 mb-4">Your cart is empty</p>
            <button
              onClick={() => navigate('/categories')}
              className="text-primary font-medium"
            >
              Continue Shopping
            </button>
          </div>
        )}

        {items.length > 0 && (
          <>
            <div className="bg-white rounded-xl p-4 shadow-sm mb-6">
              <div className="space-y-3">
                <div className="flex justify-between text-lg font-semibold">
                  <span>Subtotal</span>
                  <span className="text-primary">₹{subtotal.toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div className="bg-primary/10 rounded-xl p-4 mb-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-primary flex-shrink-0 mt-1" />
                <p className="text-sm text-gray-600">
                  Farm items are confirmed after harvest. If there's limited availability,
                  we'll notify you and adjust the final bill accordingly.
                </p>
              </div>
            </div>
          </>
        )}
      </div>

      {items.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
          <div className="md:max-w-2xl md:mx-auto">
            <button
              onClick={() => navigate('/checkout')}
              className="w-full bg-primary text-white py-4 rounded-xl font-semibold hover:bg-primary/90 transition-colors shadow-md"
            >
              Proceed to Checkout • ₹{subtotal.toFixed(2)}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}