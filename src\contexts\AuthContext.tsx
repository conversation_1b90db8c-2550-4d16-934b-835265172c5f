import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User as FirebaseUser } from 'firebase/auth';
import {
  auth,
  onAuthChange,
  signOut as firebaseSignOut,
  getCurrentUserData,
  checkUserExists,
  createUser,
  updateUserProfile
} from '../firebase/auth';
import { User } from '../firebase/schema';

interface AuthContextType {
  currentUser: FirebaseUser | null;
  userData: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  logout: () => Promise<void>;
  refreshUserData: () => Promise<void>;
  createUserProfile: (displayName: string, phoneNumber: string, address?: Partial<User['listOfAddress'][0]>) => Promise<void>;
  updateUser: (data: Partial<User>) => Promise<void>;
  getUserPhoneNumber: () => string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [currentUser, setCurrentUser] = useState<FirebaseUser | null>(null);
  const [userData, setUserData] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthChange((user) => {
      setCurrentUser(user);
      setIsLoading(false);
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    const fetchUserData = async () => {
      if (currentUser) {
        try {
          console.log('Initial fetch of user data for UID:', currentUser.uid);
          setIsLoading(true);

          const data = await getCurrentUserData();

          if (data) {
            console.log('User data fetched successfully:', data);
            setUserData(data);
          } else {
            console.warn('No user data found for UID:', currentUser.uid);
            setUserData(null);
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          setUserData(null);
        } finally {
          setIsLoading(false);
        }
      } else {
        console.log('No current user, clearing user data');
        setUserData(null);
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [currentUser]);

  const refreshUserData = async () => {
    if (currentUser) {
      try {
        console.log('Refreshing user data for UID:', currentUser.uid);
        setIsLoading(true);

        const data = await getCurrentUserData();

        if (!data) {
          console.error('Failed to fetch user data after refresh');
          throw new Error('Failed to fetch user data');
        }

        console.log('User data refreshed successfully:', data);
        setUserData(data);
        return data;
      } catch (error) {
        console.error('Error refreshing user data:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    }

    return null;
  };

  const logout = async () => {
    try {
      await firebaseSignOut();
      setUserData(null);
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const createUserProfile = async (
    displayName: string,
    phoneNumber: string,
    address?: Partial<User['listOfAddress'][0]>
  ) => {
    if (!currentUser) return;

    try {
      await createUser(currentUser.uid, displayName, phoneNumber, address);
      await refreshUserData();
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  };

  const updateUser = async (data: Partial<User>) => {
    if (!currentUser) return;

    try {
      await updateUserProfile(currentUser.uid, data);
      await refreshUserData();
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  };

  const getUserPhoneNumber = (): string | null => {
    if (!userData) {
      return null;
    }

    // Check if phone number is stored directly on user (with underscore)
    if ((userData as any).phone_number) {
      return (userData as any).phone_number;
    }

    // Check if phone number is stored directly on user (camelCase)
    if ((userData as any).phoneNumber) {
      return (userData as any).phoneNumber;
    }

    // Fallback: check addresses
    if (userData.listOfAddress && userData.listOfAddress.length > 0) {
      for (const address of userData.listOfAddress) {
        if (address.phoneNumber) {
          return address.phoneNumber;
        }
      }
    }

    return null;
  };

  const value = {
    currentUser,
    userData,
    isLoading,
    isAuthenticated: !!currentUser,
    logout,
    refreshUserData,
    createUserProfile,
    updateUser,
    getUserPhoneNumber
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
