import React, { useState, useCallback, useRef, useEffect } from 'react';
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MapPin, 
  <PERSON>hair, 
  Loader2, 
  <PERSON>ert<PERSON><PERSON>cle, 
  Check,
  X,
  Navigation
} from 'lucide-react';
import { GOOGLE_MAPS_CONFIG, DEFAULT_MAP_CONFIG, MAP_STYLES, MARKER_CONFIG } from '../../config/maps';
import { 
  getCurrentLocation, 
  reverseGeocode, 
  LocationCoordinates, 
  LocationResult,
  extractPincode,
  formatAddressForDisplay
} from '../../utils/location';

interface GoogleMapPickerProps {
  isOpen: boolean;
  onClose: () => void;
  onLocationSelect: (location: LocationResult) => void;
  initialLocation?: LocationCoordinates;
  title?: string;
}

export const GoogleMapPicker: React.FC<GoogleMapPickerProps> = ({
  isOpen,
  onClose,
  onLocationSelect,
  initialLocation,
  title = 'Select Location'
}) => {
  const [mapCenter, setMapCenter] = useState<LocationCoordinates>(
    initialLocation || DEFAULT_MAP_CONFIG.center
  );
  const [markerPosition, setMarkerPosition] = useState<LocationCoordinates>(
    initialLocation || DEFAULT_MAP_CONFIG.center
  );
  const [selectedAddress, setSelectedAddress] = useState<string>('');
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);

  const mapRef = useRef<google.maps.Map | null>(null);
  const geocoderRef = useRef<google.maps.Geocoder | null>(null);

  // Initialize geocoder when map loads
  const onMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
    geocoderRef.current = new google.maps.Geocoder();
    setIsMapLoaded(true);
    
    // Get address for initial position
    if (geocoderRef.current) {
      handleReverseGeocode(markerPosition);
    }
  }, [markerPosition]);

  // Handle reverse geocoding
  const handleReverseGeocode = useCallback(async (coordinates: LocationCoordinates) => {
    if (!geocoderRef.current) return;

    setIsLoadingAddress(true);
    setError(null);

    try {
      const result = await reverseGeocode(coordinates, geocoderRef.current);
      setSelectedAddress(result.formattedAddress);
    } catch (err: any) {
      setError(err.message || 'Unable to get address for this location');
      setSelectedAddress('');
    } finally {
      setIsLoadingAddress(false);
    }
  }, []);

  // Handle marker drag
  const onMarkerDragEnd = useCallback((e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      const newPosition = {
        lat: e.latLng.lat(),
        lng: e.latLng.lng(),
      };
      setMarkerPosition(newPosition);
      handleReverseGeocode(newPosition);
    }
  }, [handleReverseGeocode]);

  // Handle map click
  const onMapClick = useCallback((e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      const newPosition = {
        lat: e.latLng.lat(),
        lng: e.latLng.lng(),
      };
      setMarkerPosition(newPosition);
      handleReverseGeocode(newPosition);
    }
  }, [handleReverseGeocode]);

  // Get current location
  const handleGetCurrentLocation = useCallback(async () => {
    setIsLoadingLocation(true);
    setError(null);

    try {
      const coordinates = await getCurrentLocation();
      setMapCenter(coordinates);
      setMarkerPosition(coordinates);
      
      if (mapRef.current) {
        mapRef.current.panTo(coordinates);
        mapRef.current.setZoom(16);
      }
      
      handleReverseGeocode(coordinates);
    } catch (err: any) {
      setError(err.message || 'Unable to get your current location');
    } finally {
      setIsLoadingLocation(false);
    }
  }, [handleReverseGeocode]);

  // Confirm location selection
  const handleConfirmLocation = useCallback(async () => {
    if (!selectedAddress || !geocoderRef.current) return;

    try {
      const result = await reverseGeocode(markerPosition, geocoderRef.current);
      onLocationSelect(result);
      onClose();
    } catch (err: any) {
      setError(err.message || 'Unable to confirm this location');
    }
  }, [selectedAddress, markerPosition, onLocationSelect, onClose]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4"
        onClick={(e) => e.target === e.currentTarget && onClose()}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <MapPin className="w-5 h-5 text-[#73338A]" />
              <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Map Container */}
          <div className="flex-1 relative">
            <LoadScript
              googleMapsApiKey={GOOGLE_MAPS_CONFIG.apiKey}
              libraries={GOOGLE_MAPS_CONFIG.libraries}
              region={GOOGLE_MAPS_CONFIG.region}
              language={GOOGLE_MAPS_CONFIG.language}
            >
              <GoogleMap
                mapContainerStyle={{ width: '100%', height: '100%' }}
                center={mapCenter}
                zoom={DEFAULT_MAP_CONFIG.zoom}
                options={{
                  ...DEFAULT_MAP_CONFIG,
                  styles: MAP_STYLES,
                }}
                onLoad={onMapLoad}
                onClick={onMapClick}
              >
                <Marker
                  position={markerPosition}
                  draggable={MARKER_CONFIG.draggable}
                  onDragEnd={onMarkerDragEnd}
                  icon={MARKER_CONFIG.icon}
                />
              </GoogleMap>
            </LoadScript>

            {/* Current Location Button */}
            <button
              onClick={handleGetCurrentLocation}
              disabled={isLoadingLocation}
              className="absolute top-4 right-4 bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              {isLoadingLocation ? (
                <Loader2 className="w-5 h-5 text-[#73338A] animate-spin" />
              ) : (
                <Navigation className="w-5 h-5 text-[#73338A]" />
              )}
            </button>

            {/* Center Crosshair */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <Crosshair className="w-8 h-8 text-[#73338A] opacity-50" />
            </div>
          </div>

          {/* Address Display & Actions */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selected Address
              </label>
              <div className="bg-white border border-gray-300 rounded-lg p-3 min-h-[60px] flex items-center">
                {isLoadingAddress ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="w-4 h-4 text-[#73338A] animate-spin" />
                    <span className="text-gray-500">Getting address...</span>
                  </div>
                ) : selectedAddress ? (
                  <div className="flex items-start space-x-2">
                    <MapPin className="w-4 h-4 text-[#73338A] mt-0.5 flex-shrink-0" />
                    <span className="text-gray-900 text-sm leading-relaxed">
                      {formatAddressForDisplay(selectedAddress, 200)}
                    </span>
                  </div>
                ) : error ? (
                  <div className="flex items-center space-x-2 text-red-600">
                    <AlertCircle className="w-4 h-4" />
                    <span className="text-sm">{error}</span>
                  </div>
                ) : (
                  <span className="text-gray-500 text-sm">
                    Click on the map or drag the marker to select a location
                  </span>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmLocation}
                disabled={!selectedAddress || isLoadingAddress}
                className="flex-1 px-4 py-2 bg-[#73338A] text-white rounded-lg hover:bg-[#5a2a6b] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                <Check className="w-4 h-4" />
                <span>Confirm Location</span>
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
