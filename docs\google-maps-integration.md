# Google Maps Integration

This document describes the Google Maps location selection functionality implemented in the customer app.

## Overview

The Google Maps integration allows customers to:
- Select their current location automatically
- Pick any location on an interactive map
- Get reverse geocoded addresses from coordinates
- Save location data with coordinates to Firebase

## Components

### 1. GoogleMapPicker
Main component for interactive map selection.

**Location**: `src/components/maps/GoogleMapPicker.tsx`

**Features**:
- Interactive Google Maps with marker
- Drag marker to select location
- Click map to place marker
- Current location detection
- Reverse geocoding for addresses
- Responsive modal interface

### 2. LocationSelector
Simplified component with buttons for location selection.

**Location**: `src/components/maps/LocationSelector.tsx`

**Features**:
- "Use Current Location" button
- "Select on Map" button
- Card and button variants
- Opens GoogleMapPicker when needed

## Configuration

### Environment Variables
```env
VITE_GOOGLE_MAPS_API_KEY=AIzaSyA6DZgq0n6pQ7UAtbQiuD6o1TJTx4b947s
```

### Maps Configuration
**Location**: `src/config/maps.ts`

Contains:
- API key configuration
- Default map settings (center, zoom)
- Geolocation options
- Map styles
- Marker configuration

## Utilities

### Location Utilities
**Location**: `src/utils/location.ts`

**Functions**:
- `getCurrentLocation()` - Get user's current location
- `reverseGeocode()` - Convert coordinates to address
- `extractPincode()` - Extract pincode from address components
- `checkLocationPermission()` - Check location permissions
- `requestLocationPermission()` - Request location access

## Database Schema

The Address interface has been extended to include location data:

```typescript
interface Address {
  // Existing fields...
  addressID: number;
  addressLines: string;
  addressName: string;
  // ... other fields

  // New location fields (optional for backward compatibility)
  latitude?: number;
  longitude?: number;
  formattedAddress?: string;
  placeId?: string;
}
```

## Integration Points

### 1. Profile Page - AddressBook
**Location**: `src/components/profile/AddressBook.tsx`

- Added LocationSelector in the "Add Address" form
- Automatically fills address field when location is selected
- Extracts and validates pincode from selected location

### 2. Checkout Page
**Location**: `src/components/Checkout.tsx`

- Added LocationSelector in the "Add Address" modal
- Same functionality as profile page
- Integrates with existing address validation

## Usage

### Basic Usage
```tsx
import { LocationSelector } from './maps/LocationSelector';

function MyComponent() {
  const handleLocationSelect = (location: LocationResult) => {
    console.log('Selected:', location);
    // Use location.coordinates, location.formattedAddress, etc.
  };

  return (
    <LocationSelector
      onLocationSelect={handleLocationSelect}
      variant="card" // or "button"
      showCurrentLocationOption={true}
    />
  );
}
```

### Advanced Usage with GoogleMapPicker
```tsx
import { GoogleMapPicker } from './maps/GoogleMapPicker';

function MyComponent() {
  const [isMapOpen, setIsMapOpen] = useState(false);

  const handleLocationSelect = (location: LocationResult) => {
    // Handle location selection
    setIsMapOpen(false);
  };

  return (
    <GoogleMapPicker
      isOpen={isMapOpen}
      onClose={() => setIsMapOpen(false)}
      onLocationSelect={handleLocationSelect}
      initialLocation={{ lat: 28.6139, lng: 77.2090 }}
      title="Select Delivery Location"
    />
  );
}
```

## Permissions

The app handles location permissions gracefully:
- Requests permission when "Use Current Location" is clicked
- Falls back to map selection if permission is denied
- Shows appropriate error messages for permission issues

## Error Handling

- Network errors during geocoding
- Location permission denied
- GPS unavailable
- Invalid coordinates
- API key issues

All errors are handled with user-friendly messages and fallback options.

## Testing

A test component is available at `src/components/TestMaps.tsx` for testing the Google Maps integration during development.

## Dependencies

- `@react-google-maps/api` - React wrapper for Google Maps
- `@googlemaps/js-api-loader` - Google Maps JavaScript API loader

## API Key Setup

The Google Maps API key is configured for:
- Maps JavaScript API
- Places API
- Geocoding API

Make sure these APIs are enabled in your Google Cloud Console project.
