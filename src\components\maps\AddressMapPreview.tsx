import React, { useCallback, useRef, useEffect, useState } from 'react';
import { GoogleMap, Marker } from '@react-google-maps/api';
import { MapPin, Loader2, AlertCircle } from 'lucide-react';
import { useGoogleMaps } from '../../contexts/GoogleMapsContext';
import { MARKER_CONFIG } from '../../config/maps';

interface AddressMapPreviewProps {
  latitude?: number;
  longitude?: number;
  address?: string;
  className?: string;
  size?: 'small' | 'medium';
}

const MAP_CONTAINER_STYLES = {
  small: { width: '150px', height: '100px' },
  medium: { width: '200px', height: '150px' },
};

const MAP_OPTIONS = {
  disableDefaultUI: true,
  zoomControl: false,
  streetViewControl: false,
  fullscreenControl: false,
  mapTypeControl: false,
  gestureHandling: 'none',
  clickableIcons: false,
  styles: [
    {
      featureType: 'poi',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'transit',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
  ],
};

export const AddressMapPreview: React.FC<AddressMapPreviewProps> = ({
  latitude,
  longitude,
  address,
  className = '',
  size = 'small',
}) => {
  const { isLoaded, loadError } = useGoogleMaps();
  const [mapLoaded, setMapLoaded] = useState(false);
  const mapRef = useRef<google.maps.Map | null>(null);

  // Don't render if no coordinates
  if (!latitude || !longitude) {
    return (
      <div 
        className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`}
        style={MAP_CONTAINER_STYLES[size]}
      >
        <div className="text-center">
          <MapPin className="w-6 h-6 text-gray-400 mx-auto mb-1" />
          <p className="text-xs text-gray-500">No location</p>
        </div>
      </div>
    );
  }

  const coordinates = { lat: latitude, lng: longitude };

  const onMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
    setMapLoaded(true);
  }, []);

  // Handle loading states
  if (loadError) {
    return (
      <div 
        className={`bg-red-50 rounded-lg flex items-center justify-center ${className}`}
        style={MAP_CONTAINER_STYLES[size]}
      >
        <div className="text-center">
          <AlertCircle className="w-5 h-5 text-red-500 mx-auto mb-1" />
          <p className="text-xs text-red-600">Map error</p>
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div 
        className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`}
        style={MAP_CONTAINER_STYLES[size]}
      >
        <div className="text-center">
          <Loader2 className="w-5 h-5 text-gray-400 animate-spin mx-auto mb-1" />
          <p className="text-xs text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`rounded-lg overflow-hidden border border-gray-200 ${className}`}>
      <GoogleMap
        mapContainerStyle={MAP_CONTAINER_STYLES[size]}
        center={coordinates}
        zoom={15}
        options={MAP_OPTIONS}
        onLoad={onMapLoad}
      >
        <Marker
          position={coordinates}
          icon={{
            ...MARKER_CONFIG.icon,
            scaledSize: { width: 24, height: 24 }, // Smaller marker for preview
            anchor: { x: 12, y: 24 },
          }}
        />
      </GoogleMap>
      
      {/* Optional address overlay */}
      {address && size === 'medium' && (
        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white p-2">
          <p className="text-xs truncate">{address}</p>
        </div>
      )}
    </div>
  );
};
